<?php

return [
    /*
    |--------------------------------------------------------------------------
    | PayOS Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for PayOS payment gateway integration
    |
    */

    'client_id' => env('PAYOS_CLIENT_ID'),
    'api_key' => env('PAYOS_API_KEY'),
    'checksum_key' => env('PAYOS_CHECKSUM_KEY'),
    'partner_code' => env('PAYOS_PARTNER_CODE'),
    'environment' => env('PAYOS_ENVIRONMENT', 'sandbox'),

    /*
    |--------------------------------------------------------------------------
    | PayOS URLs
    |--------------------------------------------------------------------------
    */

    'return_url' => env('APP_URL', 'http://localhost:8000') . '/payment/success',
    'cancel_url' => env('APP_URL', 'http://localhost:8000') . '/payment/cancel',
    'webhook_url' => env('APP_URL', 'http://localhost:8000') . '/api/payos/webhook',

    /*
    |--------------------------------------------------------------------------
    | Token Packages
    |--------------------------------------------------------------------------
    |
    | Define the available token packages for purchase
    |
    */

    'packages' => [
        'basic' => [
            'name' => 'Basic',
            'tokens' => 5,
            'price' => 4.99,
            'amount' => 2000, // Amount in VND for PayOS (approximately $4.99)
            'description' => 'Perfect for occasional exports'
        ],
        'standard' => [
            'name' => 'Standard',
            'tokens' => 15,
            'price' => 9.99,
            'amount' => 5000, // Amount in VND for PayOS (approximately $9.99)
            'description' => 'Most popular option'
        ],
        'premium' => [
            'name' => 'Premium',
            'tokens' => 30,
            'price' => 14.99,
            'amount' => 10000, // Amount in VND for PayOS (approximately $14.99)
            'description' => 'Best value for active users'
        ]
    ]
];
